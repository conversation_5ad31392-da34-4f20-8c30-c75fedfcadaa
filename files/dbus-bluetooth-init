#!/bin/sh
#
# 简化的D-Bus和蓝牙统一初始化脚本
#

DBUS_DAEMON=/usr/bin/dbus-daemon
BT_DAEMON=/usr/libexec/bluetooth/bluetoothd

# 创建必要的目录
mkdir -p /var/run/dbus
mkdir -p /var/lib/dbus
mkdir -p /var/run/bluetooth
mkdir -p /var/lib/bluetooth

# 生成dbus机器ID（如果不存在）
if [ ! -f /var/lib/dbus/machine-id ]; then
    /usr/bin/dbus-uuidgen > /var/lib/dbus/machine-id
fi

cleanup_bluetooth() {
    echo "清理蓝牙设备状态..."
    
    # 停止所有相关进程
    killall hciattach bluetoothd 2>/dev/null || true
    sleep 2
    
    # 强制清理HCI接口（如果存在）
    if command -v hciconfig > /dev/null; then
        hciconfig hci0 down 2>/dev/null || true
    fi
    
    # 确保串口可用
    if [ -c /dev/ttyS4 ]; then
        # 检查串口是否被占用
        if fuser /dev/ttyS4 2>/dev/null; then
            echo "串口被占用，强制释放..."
            fuser -k /dev/ttyS4 2>/dev/null || true
            sleep 1
        fi
    fi
    
    echo "设备清理完成"
}

start_services() {
    echo "启动D-Bus和蓝牙服务..."
    
    # 启动D-Bus
    if ! pgrep -f "dbus-daemon --system" > /dev/null; then
        echo "启动D-Bus系统总线..."
        $DBUS_DAEMON --system --fork
        sleep 1
    else
        echo "D-Bus已在运行"
    fi
    
    # 清理蓝牙设备状态
    cleanup_bluetooth
    
    # 启动蓝牙
    if ! pgrep -f "bluetoothd" > /dev/null; then
        echo "启动蓝牙守护进程..."
        $BT_DAEMON &
        sleep 2
        
        # 连接蓝牙硬件
        if [ -c /dev/ttyS4 ]; then
            echo "连接蓝牙硬件到 /dev/ttyS4..."
            
            # 使用更稳定的参数启动hciattach (去掉flow以避免DMA问题)
            /usr/bin/hciattach -s 1500000 /dev/ttyS4 any 1500000 nosleep > /tmp/hciattach.log 2>&1 &
            HCI_PID=$!
            sleep 3
            
            # 检查hciattach是否成功启动
            if ps | grep -q "hciattach"; then
                echo "HCI连接进程已启动"
                
                # 等待HCI接口出现
                WAIT_COUNT=0
                while [ $WAIT_COUNT -lt 10 ]; do
                    if hciconfig hci0 2>/dev/null | grep -q "hci0"; then
                        echo "HCI接口已创建"
                        break
                    fi
                    echo "等待HCI接口创建... ($((WAIT_COUNT + 1))/10)"
                    sleep 1
                    WAIT_COUNT=$((WAIT_COUNT + 1))
                done
                
                                 # 尝试启用蓝牙接口
                 if [ $WAIT_COUNT -lt 10 ]; then
                     echo "启用蓝牙接口..."
                     
                     # 多次尝试启用接口
                     UP_RETRY=0
                     while [ $UP_RETRY -lt 5 ]; do
                         if hciconfig hci0 up 2>/dev/null; then
                             sleep 1
                             # 验证接口是否真的启用成功
                             if hciconfig hci0 2>/dev/null | grep -q "UP RUNNING"; then
                                 echo "蓝牙接口已启用"
                                 hciconfig hci0
                                 break
                             fi
                         fi
                         echo "蓝牙接口启用尝试 $((UP_RETRY + 1))/5 失败，重试..."
                         sleep 2
                         UP_RETRY=$((UP_RETRY + 1))
                     done
                     
                     if [ $UP_RETRY -eq 5 ]; then
                         echo "错误: 蓝牙接口启用失败，可能是硬件通信问题"
                         echo "建议检查:"
                         echo "1. 蓝牙模块硬件连接"
                         echo "2. 串口设置是否正确"
                         echo "3. 尝试运行: $0 restart"
                         echo "hciattach日志:"
                         cat /tmp/hciattach.log 2>/dev/null || echo "无日志文件"
                         echo "内核蓝牙错误:"
                         dmesg | grep -i bluetooth | tail -5 2>/dev/null || echo "无内核日志"
                         exit 1
                     fi
                else
                    echo "警告: HCI接口未在预期时间内创建"
                    echo "hciattach日志:"
                    cat /tmp/hciattach.log 2>/dev/null || echo "无日志文件"
                fi
            else
                echo "错误: hciattach进程启动失败"
                echo "hciattach日志:"
                cat /tmp/hciattach.log 2>/dev/null || echo "无日志文件"
            fi
        else
            echo "警告: 串口设备 /dev/ttyS4 不存在"
        fi
    else
        echo "蓝牙已在运行"
    fi
    
    echo "服务启动完成"
}

stop_services() {
    echo "停止D-Bus和蓝牙服务..."
    
    # 清理蓝牙设备
    cleanup_bluetooth
    
    # 停止D-Bus并清理PID文件
    killall dbus-daemon 2>/dev/null || true
    rm -f /var/run/dbus/pid /run/dbus/pid 2>/dev/null || true
    
    echo "服务已停止"
}

status_services() {
    echo "检查服务状态..."
    
    if pgrep -f "dbus-daemon --system" > /dev/null; then
        echo "D-Bus: 运行中"
    else
        echo "D-Bus: 未运行"
    fi
    
    if pgrep -f "bluetoothd" > /dev/null; then
        echo "蓝牙守护进程: 运行中"
    else
        echo "蓝牙守护进程: 未运行"
    fi
    
    if pgrep -f "hciattach" > /dev/null; then
        echo "HCI连接: 运行中"
    else
        echo "HCI连接: 未运行"
    fi
    
    if command -v hciconfig > /dev/null; then
        if hciconfig hci0 2>/dev/null | grep -q "UP"; then
            echo "蓝牙接口: 已启用"
            hciconfig hci0 2>/dev/null
        elif hciconfig hci0 2>/dev/null | grep -q "DOWN"; then
            echo "蓝牙接口: 已创建但未启用"
            hciconfig hci0 2>/dev/null
        else
            echo "蓝牙接口: 未创建"
        fi
    fi
}

case "$1" in
    start|"")
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        stop_services
        sleep 2
        start_services
        ;;
    status)
        status_services
        ;;
    cleanup)
        cleanup_bluetooth
        ;;
    health|check)
        echo "检查蓝牙健康状态..."
        
        # 首先检查HCI接口状态
        if ! hciconfig hci0 2>/dev/null | grep -q "UP RUNNING"; then
            echo "错误: 蓝牙接口未正常运行"
            exit 1
        fi
        
        BD_ADDR=$(hciconfig hci0 2>/dev/null | grep "BD Address:" | cut -d: -f2- | xargs)
        if [ "$BD_ADDR" = "00:00:00:00:00:00" ]; then
            echo "警告: 蓝牙接口虽然启用，但BD地址无效"
            echo "建议执行: $0 restart"
            exit 1
        fi
        
        # 检查数据传输
        RX_BYTES=$(hciconfig hci0 2>/dev/null | grep "RX bytes:" | sed 's/.*RX bytes:\([0-9]*\).*/\1/')
        TX_BYTES=$(hciconfig hci0 2>/dev/null | grep "TX bytes:" | sed 's/.*TX bytes:\([0-9]*\).*/\1/')
        
        # 检查最近的错误
        CURRENT_TIME=$(cat /proc/uptime | cut -d' ' -f1 | cut -d'.' -f1)
        RECENT_ERRORS=$(dmesg | grep -i "hci.*timeout" | tail -10 | while read line; do
            TIMESTAMP=$(echo "$line" | grep -o '^\[.*\]' | tr -d '[]')
            if [ -n "$TIMESTAMP" ]; then
                TIME_DIFF=$((CURRENT_TIME - ${TIMESTAMP%.*}))
                if [ "$TIME_DIFF" -lt 300 ]; then  # 5分钟内
                    echo "$line"
                fi
            fi
        done)
        
        # 综合判断健康状态
        if [ -n "$RECENT_ERRORS" ]; then
            if [ "$RX_BYTES" -gt 0 ] && [ "$TX_BYTES" -gt 0 ]; then
                echo "注意: 虽然有历史超时错误，但当前蓝牙工作正常"
                echo "BD地址: $BD_ADDR"
                echo "数据传输: RX=$RX_BYTES bytes, TX=$TX_BYTES bytes"
                echo "历史错误:"
                echo "$RECENT_ERRORS" | head -3
                echo "如果问题持续，可执行: $0 restart"
                exit 0
            else
                echo "错误: 有超时错误且无数据传输"
                echo "$RECENT_ERRORS" | head -3
                echo "建议执行: $0 restart"
                exit 1
            fi
        else
            echo "蓝牙健康状态良好"
            echo "BD地址: $BD_ADDR"
            echo "数据传输: RX=$RX_BYTES bytes, TX=$TX_BYTES bytes"
            exit 0
        fi
        ;;
    ble-setup)
        echo "设置BLE广播服务..."

        # 检查蓝牙是否已启用
        if ! hciconfig hci0 2>/dev/null | grep -q "UP RUNNING"; then
            echo "错误: 蓝牙接口未启用，请先运行: $0 start"
            exit 1
        fi

        # 使用bluetoothctl设置BLE服务
        echo "配置BLE服务..."
        echo 'power on
discoverable on
pairable on
advertise on
quit' | bluetoothctl > /tmp/ble-setup.log 2>&1

        sleep 2

        # 验证设置
        if bluetoothctl show | grep -q "Powered: yes"; then
            echo "BLE服务配置成功"
            bluetoothctl show | grep -E "Powered|Discoverable|Pairable"
        else
            echo "BLE服务配置失败，查看日志:"
            cat /tmp/ble-setup.log
            exit 1
        fi
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|cleanup|health|ble-setup}" >&2
        exit 1
        ;;
esac

exit 0 