root@aby-box-arm:~# dmesg
[    0.000000] Booting Linux on physical CPU 0x0000000000 [0x410fd034]
[    0.000000] Linux version 5.10.4-aby (oe-user@oe-host) (aarch64-oe-linux-gcc (GCC) 14.2.0, GNU ld (GNU Binutils) 2.43.1) #1 SMP PREEMPT Thu Jun 12 10:21:29 UTC 2025
[    0.000000] Machine model: Aby Box
[    0.000000] efi: UEFI not found.
[    0.000000] Ion: Ion memory setup at 0x0000000095400000 size 170 MiB
[    0.000000] OF: reserved mem: initialized node ion, compatible id ion-region
[    0.000000] Zone ranges:
[    0.000000]   DMA      [mem 0x0000000080000000-0x000000009fdfffff]
[    0.000000]   DMA32    empty
[    0.000000]   Normal   empty
[    0.000000] Movable zone start for each node
[    0.000000] Early memory node ranges
[    0.000000]   node   0: [mem 0x0000000080000000-0x0000000094c2ffff]
[    0.000000]   node   0: [mem 0x0000000095400000-0x000000009fdfffff]
[    0.000000] Zeroed struct page in unavailable ranges: 1488 pages
[    0.000000] Initmem setup node 0 [mem 0x0000000080000000-0x000000009fdfffff]
[    0.000000] On node 0 totalpages: 128560
[    0.000000]   DMA zone: 2040 pages used for memmap
[    0.000000]   DMA zone: 0 pages reserved
[    0.000000]   DMA zone: 128560 pages, LIFO batch:31
[    0.000000] psci: probing for conduit method from DT.
[    0.000000] psci: PSCIv1.0 detected in firmware.
[    0.000000] psci: Using standard PSCI v0.2 function IDs
[    0.000000] psci: MIGRATE_INFO_TYPE not supported.
[    0.000000] psci: SMC Calling Convention v1.0
[    0.000000] percpu: Embedded 20 pages/cpu s44824 r8192 d28904 u81920
[    0.000000] pcpu-alloc: s44824 r8192 d28904 u81920 alloc=20*4096
[    0.000000] pcpu-alloc: [0] 0 
[    0.000000] Detected VIPT I-cache on CPU0
[    0.000000] CPU features: detected: ARM erratum 845719
[    0.000000] Built 1 zonelists, mobility grouping on.  Total pages: 126520
[    0.000000] Kernel command line: root=/dev/mmcblk0p3 console=ttyS0,115200 earlycon=sbi loglevel=9 rootwait rw
[    0.000000] Dentry cache hash table entries: 65536 (order: 7, 524288 bytes, linear)
[    0.000000] Inode-cache hash table entries: 32768 (order: 6, 262144 bytes, linear)
[    0.000000] mem auto-init: stack:off, heap alloc:off, heap free:off
[    0.000000] Memory: 310520K/514240K available (6208K kernel code, 618K rwdata, 1956K rodata, 1600K init, 247K bss, 203720K reserved, 0K cma-reserved)
[    0.000000] SLUB: HWalign=64, Order=0-3, MinObjects=0, CPUs=1, Nodes=1
[    0.000000] rcu: Preemptible hierarchical RCU implementation.
[    0.000000] rcu: 	RCU event tracing is enabled.
[    0.000000] rcu: 	RCU restricting CPUs from NR_CPUS=256 to nr_cpu_ids=1.
[    0.000000] 	Trampoline variant of Tasks RCU enabled.
[    0.000000] rcu: RCU calculated value of scheduler-enlistment delay is 25 jiffies.
[    0.000000] rcu: Adjusting geometry for rcu_fanout_leaf=16, nr_cpu_ids=1
[    0.000000] NR_IRQS: 64, nr_irqs: 64, preallocated irqs: 0
[    0.000000] arch_timer: cp15 timer(s) running at 25.00MHz (virt).
[    0.000000] clocksource: arch_sys_counter: mask: 0xffffffffffffff max_cycles: 0x5c40939b5, max_idle_ns: 440795202646 ns
[    0.000006] sched_clock: 56 bits at 25MHz, resolution 40ns, wraps every 4398046511100ns
[    0.000061] Calibrating delay loop (skipped), value calculated using timer frequency.. 50.00 BogoMIPS (lpj=100000)
[    0.000080] pid_max: default: 4096 minimum: 301
[    0.000225] Mount-cache hash table entries: 1024 (order: 1, 8192 bytes, linear)
[    0.000240] Mountpoint-cache hash table entries: 1024 (order: 1, 8192 bytes, linear)
[    0.001636] rcu: Hierarchical SRCU implementation.
[    0.002356] EFI services will not be available.
[    0.002573] smp: Bringing up secondary CPUs ...
[    0.002587] smp: Brought up 1 node, 1 CPU
[    0.002596] SMP: Total of 1 processors activated.
[    0.002612] CPU features: detected: 32-bit EL0 Support
[    0.002625] CPU features: detected: CRC32 instructions
[    0.002691] CPU: All CPU(s) started at EL1
[    0.002713] alternatives: patching kernel code
[    0.003358] devtmpfs: initialized
[    0.007762] early_time_log: do_initcalls: 4936729us
[    0.008016] random: get_random_u32 called from bucket_table_alloc.isra.0+0xf4/0x120 with crng_init=0
[    0.008431] clocksource: jiffies: mask: 0xffffffff max_cycles: 0xffffffff, max_idle_ns: 7645041785100000 ns
[    0.008461] futex hash table entries: 16 (order: -2, 1024 bytes, linear)
[    0.008552] pinctrl core: initialized pinctrl subsystem
[    0.008760] DMI not present or invalid.
[    0.008958] NET: Registered protocol family 16
[    0.009559] DMA: preallocated 128 KiB GFP_KERNEL pool for atomic allocations
[    0.009607] DMA: preallocated 128 KiB GFP_KERNEL|GFP_DMA pool for atomic allocations
[    0.009648] DMA: preallocated 128 KiB GFP_KERNEL|GFP_DMA32 pool for atomic allocations
[    0.010098] thermal_sys: Registered thermal governor 'step_wise'
[    0.010584] ASID allocator initialised with 65536 entries
[    0.020816] OF: /gpio@03020000/gpio-controller@0: could not find phandle
[    0.020887] OF: /gpio@03021000/gpio-controller@1: could not find phandle
[    0.020948] OF: /gpio@03022000/gpio-controller@2: could not find phandle
[    0.021006] OF: /gpio@03023000/gpio-controller@3: could not find phandle
[    0.021064] OF: /gpio@05021000/gpio-controller@4: could not find phandle
[    0.022913] clk reset: nr_reset=64 resource_size=8
[    0.023430] get audio clk=24576000
[    0.023489] cvitek-i2s-subsys 4108000.i2s_subsys: Set clk_sdma_aud0~3 to 24576000
[    0.034155] dw_dmac 4330000.dma: CVITEK DMA Controller, 8 channels, probe done!
[    0.034830] SCSI subsystem initialized
[    0.035056] usbcore: registered new interface driver usbfs
[    0.035111] usbcore: registered new interface driver hub
[    0.035213] usbcore: registered new device driver usb
[    0.038123] Ion: ion_parse_dt_heap_common: id 0 type 2 name carveout align 1000
[    0.038603] Ion: rmem_ion_device_init: heap carveout base 0x0000000095400000 size 0x000000000aa00000 dev (____ptrval____)
[    0.038618] ion_carveout_heap_create, size=0xaa00000
[    0.038746] cvi_get_rtos_ion_size, rtos ion_size get:0x1600000
[    0.038755] ion_carveout_heap_create, size(exclusion of rtos_ion_size)=0x9400000
[    0.092009] platform carveout: [ion] add heap id 0, type 2, base 0x95400000, size 0xaa00000
[    0.092392] Advanced Linux Sound Architecture Driver Initialized.
[    0.093008] Bluetooth: Core ver 2.22
[    0.093077] NET: Registered protocol family 31
[    0.093087] Bluetooth: HCI device and connection manager initialized
[    0.093105] Bluetooth: HCI socket layer initialized
[    0.093118] Bluetooth: L2CAP socket layer initialized
[    0.093140] Bluetooth: SCO socket layer initialized
[    0.093685] clocksource: Switched to clocksource arch_sys_counter
[    0.095105] NET: Registered protocol family 2
[    0.095857] tcp_listen_portaddr_hash hash table entries: 256 (order: 0, 4096 bytes, linear)
[    0.095889] TCP established hash table entries: 4096 (order: 3, 32768 bytes, linear)
[    0.095941] TCP bind hash table entries: 4096 (order: 4, 65536 bytes, linear)
[    0.096021] TCP: Hash tables configured (established 4096 bind 4096)
[    0.096134] UDP hash table entries: 256 (order: 1, 8192 bytes, linear)
[    0.096165] UDP-Lite hash table entries: 256 (order: 1, 8192 bytes, linear)
[    0.096321] NET: Registered protocol family 1
[    0.097099] RPC: Registered named UNIX socket transport module.
[    0.097119] RPC: Registered udp transport module.
[    0.097127] RPC: Registered tcp transport module.
[    0.097135] RPC: Registered tcp NFSv4.1 backchannel transport module.
[    0.099093] Initialise system trusted keyrings
[    0.099329] workingset: timestamp_bits=62 max_order=17 bucket_order=0
[    0.104209] squashfs: version 4.0 (2009/01/31) Phillip Lougher
[    0.159373] Key type asymmetric registered
[    0.159395] Asymmetric key parser 'x509' registered
[    0.159943] cvitek,pinctrl-cv181x 3001000.pinctrl: cvi_pinctrl_probe(): reg=(____ptrval____),4096 CVITEK_PINMUX_REG_LAST=0x1cc
[    0.165026] Serial: 8250/16550 driver, 5 ports, IRQ sharing disabled
[    0.166502] printk: console [ttyS0] disabled
[    0.166599] 4140000.serial: ttyS0 at MMIO 0x4140000 (irq = 23, base_baud = 1562500) is a 16550A
[    0.915358] printk: console [ttyS0] enabled
[    0.920440] 4150000.serial: ttyS1 at MMIO 0x4150000 (irq = 24, base_baud = 1562500) is a 16550A
[    0.930306] 4170000.serial: ttyS3 at MMIO 0x4170000 (irq = 25, base_baud = 1562500) is a 16550A
[    0.940101] 41c0000.serial: ttyS4 at MMIO 0x41c0000 (irq = 26, base_baud = 1562500) is a 16550A
[    0.956125] loop: module loaded
[    0.959979] zram: Added device: zram0
[    0.964852] cvi-spif 10000000.cvi-spif: unrecognized JEDEC id bytes: ff ff ff ff ff ff
[    0.973070] cvi-spif 10000000.cvi-spif: device scan failed
[    0.978747] cvi-spif 10000000.cvi-spif: unable to setup flash chip
[    0.986505] libphy: Fixed MDIO Bus: probed
[    0.991416] bm-dwmac 4070000.ethernet: IRQ eth_wake_irq not found
[    0.997767] bm-dwmac 4070000.ethernet: IRQ eth_lpi not found
[    1.003711] bm-dwmac 4070000.ethernet: Hash table entries set to unexpected value 0
[    1.011766] bm-dwmac 4070000.ethernet: no reset control found
[    1.017930] bm-dwmac 4070000.ethernet: User ID: 0x10, Synopsys ID: 0x37
[    1.024811] bm-dwmac 4070000.ethernet: 	DWMAC1000
[    1.029683] bm-dwmac 4070000.ethernet: DMA HW capability register supported
[    1.036882] bm-dwmac 4070000.ethernet: RX Checksum Offload Engine supported
[    1.044081] bm-dwmac 4070000.ethernet: COE Type 2
[    1.048948] bm-dwmac 4070000.ethernet: TX Checksum insertion supported
[    1.055699] bm-dwmac 4070000.ethernet: Normal descriptors
[    1.061322] bm-dwmac 4070000.ethernet: Ring mode enabled
[    1.066819] bm-dwmac 4070000.ethernet: Enable RX Mitigation via HW Watchdog Timer
[    1.074563] bm-dwmac 4070000.ethernet: device MAC address 8a:32:83:69:67:97
[    1.104534] libphy: stmmac: probed
[    1.109474] bm-dwmac 4070000.ethernet: Cannot get clk_500m_eth!
[    1.115857] bm-dwmac 4070000.ethernet: Cannot get gate_clk_axi4!
[    1.122412] usbcore: registered new interface driver asix
[    1.128127] usbcore: registered new interface driver ax88179_178a
[    1.134481] usbcore: registered new interface driver cdc_ether
[    1.140563] usbcore: registered new interface driver smsc95xx
[    1.146540] usbcore: registered new interface driver net1080
[    1.152425] usbcore: registered new interface driver cdc_subset
[    1.158583] usbcore: registered new interface driver zaurus
[    1.164398] usbcore: registered new interface driver cdc_ncm
[    1.171116] dwc2 4340000.usb: axi clk installed
[    1.175850] dwc2 4340000.usb: apb clk installed
[    1.180545] dwc2 4340000.usb: 125m clk installed
[    1.185327] dwc2 4340000.usb: 33k clk installed
[    1.190017] dwc2 4340000.usb: 12m clk installed
[    1.194778] dwc2 4340000.usb: EPs: 8, dedicated fifos, 3072 entries in SPRAM
[    1.202364] dwc2 4340000.usb: DWC OTG Controller
[    1.207193] dwc2 4340000.usb: new USB bus registered, assigned bus number 1
[    1.214425] dwc2 4340000.usb: irq 48, io mem 0x04340000
[    1.220610] hub 1-0:1.0: USB hub found
[    1.224611] hub 1-0:1.0: 1 port detected
[    1.230132] usbcore: registered new interface driver uas
[    1.235778] usbcore: registered new interface driver usb-storage
[    1.242337] i2c /dev entries driver
[    1.247205] Bluetooth: HCI UART driver ver 2.3
[    1.251859] Bluetooth: HCI UART protocol H4 registered
[    1.257574] sdhci: Secure Digital Host Controller Interface driver
[    1.263984] sdhci: Copyright(c) Pierre Ossman
[    1.268493] sdhci-pltfm: SDHCI platform and OF driver helper
[    1.274562] cvi:sdhci_cvi_probe
[    1.321688] mmc0: SDHCI controller on 4310000.cv-sd [4310000.cv-sd] using ADMA 64-bit
[    1.330060] cvi_proc_init cvi_host 0x(____ptrval____)
[    1.335497] cvi:sdhci_cvi_probe
[    1.347938] mmc1 bounce up to 128 segments into one, max segment size 65536 bytes
[    1.386229] mmc1: SDHCI controller on 4320000.wifi-sd [4320000.wifi-sd] using DMA
[    1.394673] usbcore: registered new interface driver usbhid
[    1.400627] usbhid: USB HID core driver
[    1.405367] usbcore: registered new interface driver snd-usb-audio
[    1.412703] cvitek-i2s 4100000.i2s: cvi_i2s_probe
[    1.418353] cvitek-i2s 4110000.i2s: cvi_i2s_probe
[    1.423961] cvitek-i2s 4120000.i2s: cvi_i2s_probe
[    1.434258] cvitek-i2s 4130000.i2s: cvi_i2s_probe
[    1.441068] cv1835-pdm sound_PDM: cv1835_pdm_probe, dev name=sound_PDM
[    1.448329] cv1835pdm 41d0c00.pdm: cv1835pdm_probe
[    1.458285] cviteka-adc sound_adc: cviteka_adc_probe, dev name=sound_adc
[    1.465402] cviteka-adc sound_adc: cviteka_adc_probe start devm_snd_soc_register_card
[    1.477790] cvitekaadc 300a100.adc: cvitekaadc_probe
[    1.483830] cviteka-dac sound_dac: cviteka_dac_probe, dev name=sound_dac
[    1.491325] cvitekadac 300a000.dac: cvitekadac_probe
[    1.501212] cvitekadac_probe gpio_is_valid mute_pin_l
[    1.513676] cvitekadac_probe gpio_is_valid mute_pin_r
[    1.519889] NET: Registered protocol family 10
[    1.527142] Segment Routing with IPv6
[    1.531225] sit: IPv6, IPv4 and MPLS over IPv4 tunneling driver
[    1.540380] NET: Registered protocol family 17
[    1.545399] Bluetooth: RFCOMM TTY layer initialized
[    1.550701] Bluetooth: RFCOMM socket layer initialized
[    1.556284] Bluetooth: RFCOMM ver 1.11
[    1.560339] Bluetooth: BNEP (Ethernet Emulation) ver 1.3
[    1.566027] Bluetooth: BNEP filters: protocol multicast
[    1.571663] Bluetooth: BNEP socket layer initialized
[    1.576872] Bluetooth: HIDP (Human Interface Emulation) ver 1.2
[    1.583058] Bluetooth: HIDP socket layer initialized
[    1.588674] Loading compiled-in X.509 certificates
[    1.595852] random: fast init done
[    1.639487] cv1835-pdm sound_PDM: cv1835_pdm_probe, dev name=sound_PDM
[    1.647862] cv1835pdm_component_probe
[    1.652433] cv1835-pdm sound_PDM: ASoC: no DMI vendor name!
[    1.659682] cviteka-adc sound_adc: cviteka_adc_probe, dev name=sound_adc
[    1.666697] cviteka-adc sound_adc: cviteka_adc_probe start devm_snd_soc_register_card
[    1.675391] cviteka-adc sound_adc: ASoC: no DMI vendor name!
[    1.683810] cviteka-dac sound_dac: cviteka_dac_probe, dev name=sound_dac
[    1.691370] cviteka-dac sound_dac: ASoC: no DMI vendor name!
[    1.699370] cfg80211: Loading compiled-in X.509 certificates for regulatory database
[    1.711445] cfg80211: Loaded X.509 cert 'sforshee: 00b28ddf47aef9cea7'
[    1.718524] cfg80211: failed to load regulatory.db
[    1.723964] ALSA device list:
[    1.727750] dw-apb-uart 4140000.serial: forbid DMA for kernel console
[    1.734716] Waiting for root device /dev/mmcblk0p3...
[    2.260930] mmc0: new ultra high speed SDR104 SDXC card at address aaaa
[    2.268471] mmcblk0: mmc0:aaaa SP64G 59.5 GiB 
[    2.277965]  mmcblk0: p1 p2 p3 p4
[    2.358220] random: crng init done
[    2.557082] EXT4-fs (mmcblk0p3): recovery complete
[    2.565226] EXT4-fs (mmcblk0p3): mounted filesystem with ordered data mode. Opts: (null)
[    2.573709] VFS: Mounted root (ext4 filesystem) on device 179:3.
[    2.582480] devtmpfs: mounted
[    2.586661] Freeing unused kernel memory: 1600K
[    2.591521] Run /sbin/init as init process
[    2.595834]   with arguments:
[    2.598915]     /sbin/init
[    2.601720]   with environment:
[    2.604965]     HOME=/
[    2.607412]     TERM=linux
[    2.610218] early_time_log: run_init_process: 7539194us
[    4.498471] g_ether: bad vermagic: kernel tainted.
[    4.503479] Disabling lock debugging due to kernel taint
[    4.509975] using random self ethernet address
[    4.514608] using random host ethernet address
[    4.520156] usb0: HOST MAC 36:1b:10:d4:8e:ff
[    4.524968] usb0: MAC 82:3b:f3:18:1a:07
[    4.529181] using random self ethernet address
[    4.533950] using random host ethernet address
[    4.538775] g_ether gadget: Ethernet Gadget, version: Memorial Day 2008
[    4.545820] g_ether gadget: g_ether ready
[    4.550112] dwc2 4340000.usb: bound driver g_ether
[    4.704348] bm-dwmac 4070000.ethernet eth0: validation of rmii with support 0000000,00000000,00006280 and advertisement 0000000,00000000,00006280 failed: -22
[    4.719303] bm-dwmac 4070000.ethernet eth0: stmmac_open: Cannot attach to PHY (error: -22)
[    4.755218] dwc2 4340000.usb: new device is high-speed
[    4.861765] CVITEK CHIP ID = 15
[    4.869291] dwc2 4340000.usb: new device is high-speed
[    4.892391] cvi_rtos_cmdqu_probe start ---
[    4.896688] name=1900000.rtos_cmdqu
[    4.900779] res-reg: start: 0x1900000, end: 0x1900fff, virt-addr(ffffffc010c45000).
[    4.909024] cvi_rtos_cmdqu_probe DONE
[    4.913236] [cvi_spinlock_init] success
[    4.928254] dwc2 4340000.usb: new address 8
[    4.950620] IPv6: ADDRCONF(NETDEV_CHANGE): usb0: link becomes ready
[    5.145702] RTOS_CMDQU_SEND_WAIT timeout
[    5.149783] SYS_CMD_INFO_LINUX_INIT_DONE fail
[    5.154596] communicate with rtos fail
[    5.190934] cif a0c2000.cif: cam0 clk installed
[    5.195685] cif a0c2000.cif: cam1 clk installed
[    5.200678] cif a0c2000.cif: vip_sys_2 clk installed
[    5.206014] cif a0c2000.cif: clk_mipimpll clk installed 0000000078a736d7
[    5.213116] cif a0c2000.cif: clk_disppll clk installed 000000003849cabc
[    5.220135] cif a0c2000.cif: clk_fpll clk installed 0000000008d581fd
[    5.226884] cif a0c2000.cif: (0) res-reg: start: 0xa0c2000, end: 0xa0c3fff.
[    5.234231] cif a0c2000.cif:  virt-addr(00000000604569be)
[    5.239963] cif a0c2000.cif: (1) res-reg: start: 0xa0d0000, end: 0xa0d0fff.
[    5.247301] cif a0c2000.cif:  virt-addr(0000000040da044d)
[    5.253036] cif a0c2000.cif: (2) res-reg: start: 0xa0c4000, end: 0xa0c5fff.
[    5.260373] cif a0c2000.cif:  virt-addr(000000008b236fe9)
[    5.266258] cif a0c2000.cif: (3) res-reg: start: 0xa0c6000, end: 0xa0c7fff.
[    5.273621] cif a0c2000.cif:  virt-addr(000000000695281a)
[    5.279358] cif a0c2000.cif: (4) res-reg: start: 0x3001c30, end: 0x3001c5f.
[    5.286697] cif a0c2000.cif:  virt-addr(00000000f1de43ee)
[    5.292402] cif a0c2000.cif: no pad_ctrl for cif
[    5.297398] cif a0c2000.cif: request irq-37 as cif-irq0
[    5.303071] cif a0c2000.cif: request irq-38 as cif-irq1
[    5.308698] cif a0c2000.cif: rst_pin = 482, pol = 1
[    5.329206] snsr_i2c snsr_i2c: i2c:-------hook 1
[    5.334217] snsr_i2c snsr_i2c: i2c:-------hook 2
[    5.339394] snsr_i2c snsr_i2c: i2c:-------hook 3
[    5.456377] vi_core_probe:193(): res-reg: start: 0xa000000, end: 0xa07ffff, virt-addr(ffffffc010f00000).
[    5.466261] vi_core_probe:206(): irq(39) for isp get from platform driver.
[    5.474247] sync_task_init:177(): sync_task_init vi_pipe 0
[    5.479964] sync_task_init:177(): sync_task_init vi_pipe 1
[    5.485892] sync_task_init:177(): sync_task_init vi_pipe 2
[    5.491706] sync_task_init:177(): sync_task_init vi_pipe 3
[    5.497509] sync_task_init:177(): sync_task_init vi_pipe 4
[    5.503705] vi_core_probe:242(): isp registered as cvi-vi
[    5.629906] vpss_start_handler:5134(): handler for dev(0) started
[    5.630027] vpss_start_handler:5134(): handler for dev(1) started
[    5.722744] cvi-mipi-tx mipi_tx: IRQ index 0 not found
[    5.734515] cvi-mipi-tx mipi_tx: vbat irq(-6)
[    5.739348] cvi-mipi-tx mipi_tx: reset gpio pin(354) active(0)
[    5.745539] cvi-mipi-tx mipi_tx: power ctrl gpio pin(353) active(1)
[    5.752150] cvi-mipi-tx mipi_tx: pwm gpio pin(352) active(1)
[    5.818882] cv181x-cooling cv181x_cooling: elems of dev-freqs=6
[    5.825098] cv181x-cooling cv181x_cooling: dev_freqs[0]: 800000000 500000000
[    5.832857] cv181x-cooling cv181x_cooling: dev_freqs[1]: 400000000 375000000
[    5.840336] cv181x-cooling cv181x_cooling: dev_freqs[2]: 400000000 300000000
[    5.847874] cv181x-cooling cv181x_cooling: Cooling device registered: cv181x_cooling
[    5.903187] [INFO] Register SBM IRQ ###################################
[    5.903213] [INFO] pvctx->s_sbm_irq = 45
[    5.927788] jpu ctrl reg pa = 0xb030000, va = 000000001bd0ab01, size = 256
[    5.939343] end jpu_init result = 0x0
[    6.117399] cvi_vc_drv_init result = 0x0
[    6.188912] pstStCandiCornerCtrl->stMem.u32Size must be greater than or equal to 16400!
[    7.233705] i2c_designware 4010000.i2c: controller timed out
[    7.239597] ltr303 1-0029: can't get Part ID: -110
[    7.244929] ltr303: probe of 1-0029 failed with error -110
[    7.290638] aicbsp_init
[    7.293181] RELEASE_DATE:2024_0327_3561b08f
[    7.297922] aicbsp_resv_mem_init 
[    7.446386] aicbsp: aicbsp_set_subsys, subsys: AIC_WIFI, state to: 1
[    7.452998] aicbsp: aicbsp_set_subsys, power state change to 1 dure to AIC_WIFI
[    7.460836] aicbsp: aicbsp_platform_power_on
[    7.465408] ======== CVITEK WLAN_POWER_ON ========
[    7.520578] aicbsp_platform_power_on,539: cvi_sdio_rescan
[    7.573075] mmc1: new high speed SDIO card at address 390b
[    7.594530] aicbsp: aicbsp_sdio_probe:1 vid:0xC8A1  did:0x0082
[    7.600705] aicbsp: aicbsp_sdio_probe:2 vid:0xC8A1  did:0x0182
[    7.607021] aicbsp: aicbsp_sdio_probe after replace:1
[    7.612421] aicbsp: aicbsp_get_feature, set FEATURE_SDIO_CLOCK 150 MHz
[    7.619307] aicbsp: aicwf_sdio_reg_init
[    7.624307] rwnx_load_firmware :firmware path = /etc/firmware/aic8800/fw_patch_table_8800d80_u02.bin  
[    7.638286] file md5:0c9bf9c9c10f7a90a22a4c35fa58c967
[    7.644413] rwnx_plat_bin_fw_upload_android
[    7.649052] rwnx_load_firmware :firmware path = /etc/firmware/aic8800/fw_adid_8800d80_u02.bin  
[    7.660836] file md5:f546881a81b960d89a672578eb45a809
[    7.667247] rwnx_plat_bin_fw_upload_android
[    7.671829] rwnx_load_firmware :firmware path = /etc/firmware/aic8800/fw_patch_8800d80_u02.bin  
[    7.690357] file md5:35d137b8a76daaeb4f5034df8e15bcde
[    7.721910] aicbt_patch_table_load bt btmode[3]:5 
[    7.726984] aicbt_patch_table_load bt uart_baud[3]:1500000 
[    7.733085] aicbt_patch_table_load bt uart_flowctrl[3]:1 
[    7.738914] aicbt_patch_table_load bt lpm_enable[3]:0 
[    7.744448] aicbt_patch_table_load bt tx_pwr[3]:28463 
[    7.757746] aicbsp: bt patch version: - Mar 07 2024 14:29:05 - git f94a3e4
[    7.764961] rwnx_plat_bin_fw_upload_android
[    7.769545] rwnx_load_firmware :firmware path = /etc/firmware/aic8800/fmacfw_8800d80_u02.bin  
[    7.856732] file md5:13e6f0e58aae342d260d8672ab61c31f
[    7.955395] rd_version_val=06090101
[    7.975106] aicbsp: aicbsp_get_feature, set FEATURE_SDIO_CLOCK 150 MHz
[    7.981888] aicsdio: aicwf_sdio_reg_init
[    7.991417] aicbsp: aicbsp_resv_mem_alloc_skb, alloc resv_mem_txdata succuss, id: 0, size: 98304
[    8.000906] aicbsp: aicbsp_get_feature, set FEATURE_SDIO_CLOCK 150 MHz
[    8.009730] aicbsp: sdio_err:<aicwf_sdio_bus_pwrctl,1431>: bus down
[    8.016987] AICWFDBG(LOGERROR)	invalid cmd: lvl_adj_5g_chan_42
[    8.023077] AICWFDBG(LOGERROR)	invalid cmd: lvl_adj_5g_chan_58
[    8.029350] AICWFDBG(LOGERROR)	invalid cmd: lvl_adj_5g_chan_106
[    8.035641] AICWFDBG(LOGERROR)	invalid cmd: lvl_adj_5g_chan_122
[    8.042014] AICWFDBG(LOGERROR)	invalid cmd: lvl_adj_5g_chan_138
[    8.048315] AICWFDBG(LOGERROR)	invalid cmd: lvl_adj_5g_chan_155
[    8.668949] ieee80211 phy0: HT supp 1, VHT supp 1, HE supp 1
[    8.706016] rcS (148): drop_caches: 3
[    9.783659] EXT4-fs (mmcblk0p4): recovery complete
[    9.792094] EXT4-fs (mmcblk0p4): mounted filesystem with ordered data mode. Opts: (null)
[   11.901736] rwnx_virtual_interface_add: 10, p2p-dev-wlan0
[   11.907347] rwnx_virtual_interface_add, ifname=p2p-dev-wlan0, wdev=0000000092a2112a, vif_idx=1
[   11.916511] p2p dev addr=88 0 33 77 fc 91
[   12.056827] P2P interface started
[   13.626684] rwnx_send_sm_connect_req drv_vif_index:0 connect to Arch(4) channel:5240 auth_type:0
