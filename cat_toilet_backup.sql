-- MySQL dump 10.13  Distrib 8.0.41, for Linux (x86_64)
--
-- Host: localhost    Database: cat_toilet_db
-- ------------------------------------------------------
-- Server version	8.0.41-0ubuntu0.22.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `cat_alerts`
--

DROP TABLE IF EXISTS `cat_alerts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cat_alerts` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `cat_id` bigint DEFAULT NULL,
  `type` varchar(32) DEFAULT NULL,
  `level` tinyint DEFAULT '1',
  `title` varchar(128) DEFAULT NULL,
  `description` text,
  `suggestions` text,
  `status` tinyint DEFAULT '1',
  `process_time` timestamp NULL DEFAULT NULL,
  `process_note` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_cat_id` (`cat_id`),
  CONSTRAINT `cat_alerts_ibfk_1` FOREIGN KEY (`cat_id`) REFERENCES `cats` (`cat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cat_alerts`
--

LOCK TABLES `cat_alerts` WRITE;
/*!40000 ALTER TABLE `cat_alerts` DISABLE KEYS */;
/*!40000 ALTER TABLE `cat_alerts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cat_behaviors`
--

DROP TABLE IF EXISTS `cat_behaviors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cat_behaviors` (
  `cat_id` bigint NOT NULL,
  `toilet_frequency` int DEFAULT NULL,
  `diet_preference` text,
  `activity_level` tinyint DEFAULT '2',
  `indoor_only` tinyint(1) DEFAULT '1',
  `facial_features` text,
  `body_features` text,
  `behavior_tags` text,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`cat_id`),
  CONSTRAINT `cat_behaviors_ibfk_1` FOREIGN KEY (`cat_id`) REFERENCES `cats` (`cat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cat_behaviors`
--

LOCK TABLES `cat_behaviors` WRITE;
/*!40000 ALTER TABLE `cat_behaviors` DISABLE KEYS */;
/*!40000 ALTER TABLE `cat_behaviors` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cat_health`
--

DROP TABLE IF EXISTS `cat_health`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cat_health` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `cat_id` bigint DEFAULT NULL,
  `type` varchar(32) DEFAULT NULL,
  `weight` decimal(5,2) DEFAULT NULL,
  `temperature` decimal(3,1) DEFAULT NULL,
  `description` text,
  `attachments` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_cat_id` (`cat_id`),
  CONSTRAINT `cat_health_ibfk_1` FOREIGN KEY (`cat_id`) REFERENCES `cats` (`cat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cat_health`
--

LOCK TABLES `cat_health` WRITE;
/*!40000 ALTER TABLE `cat_health` DISABLE KEYS */;
/*!40000 ALTER TABLE `cat_health` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cat_metrics_daily`
--

DROP TABLE IF EXISTS `cat_metrics_daily`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cat_metrics_daily` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `cat_id` bigint NOT NULL,
  `metric_date` date NOT NULL,
  `weight` decimal(5,2) DEFAULT NULL,
  `weight_min` decimal(5,2) DEFAULT NULL,
  `weight_max` decimal(5,2) DEFAULT NULL,
  `weight_variance` decimal(5,3) DEFAULT NULL,
  `toilet_count` int DEFAULT '0',
  `urine_count` int DEFAULT '0',
  `stool_count` int DEFAULT '0',
  `abnormal_count` int DEFAULT '0',
  `total_duration` int DEFAULT '0',
  `avg_duration` int DEFAULT '0',
  `total_waste_weight` decimal(6,3) DEFAULT NULL,
  `avg_waste_weight` decimal(5,3) DEFAULT NULL,
  `morning_count` int DEFAULT NULL,
  `afternoon_count` int DEFAULT NULL,
  `evening_count` int DEFAULT NULL,
  `night_count` int DEFAULT NULL,
  `health_score` decimal(4,1) DEFAULT NULL,
  `hydration_level` decimal(3,2) DEFAULT NULL,
  `digestion_score` decimal(3,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_cat_date` (`cat_id`,`metric_date`),
  CONSTRAINT `cat_metrics_daily_ibfk_1` FOREIGN KEY (`cat_id`) REFERENCES `cats` (`cat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cat_metrics_daily`
--

LOCK TABLES `cat_metrics_daily` WRITE;
/*!40000 ALTER TABLE `cat_metrics_daily` DISABLE KEYS */;
/*!40000 ALTER TABLE `cat_metrics_daily` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cat_metrics_monthly`
--

DROP TABLE IF EXISTS `cat_metrics_monthly`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cat_metrics_monthly` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `cat_id` bigint NOT NULL,
  `year` int NOT NULL,
  `month` int NOT NULL,
  `avg_weight` decimal(5,2) DEFAULT NULL,
  `weight_trend` decimal(4,2) DEFAULT NULL,
  `total_toilet_count` int DEFAULT NULL,
  `avg_toilet_count` decimal(4,1) DEFAULT NULL,
  `total_abnormal` int DEFAULT NULL,
  `health_score` decimal(4,1) DEFAULT NULL,
  `health_trend` decimal(4,1) DEFAULT NULL,
  `suggestions` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_cat_month` (`cat_id`,`year`,`month`),
  CONSTRAINT `cat_metrics_monthly_ibfk_1` FOREIGN KEY (`cat_id`) REFERENCES `cats` (`cat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cat_metrics_monthly`
--

LOCK TABLES `cat_metrics_monthly` WRITE;
/*!40000 ALTER TABLE `cat_metrics_monthly` DISABLE KEYS */;
/*!40000 ALTER TABLE `cat_metrics_monthly` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cat_profiles`
--

DROP TABLE IF EXISTS `cat_profiles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cat_profiles` (
  `cat_id` bigint NOT NULL,
  `is_sterilized` tinyint(1) DEFAULT '0',
  `sterilized_date` date DEFAULT NULL,
  `source` varchar(32) DEFAULT NULL,
  `chip_number` varchar(64) DEFAULT NULL,
  `blood_type` varchar(8) DEFAULT NULL,
  `allergies` text,
  `chronic_diseases` text,
  `notes` text,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`cat_id`),
  CONSTRAINT `cat_profiles_ibfk_1` FOREIGN KEY (`cat_id`) REFERENCES `cats` (`cat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cat_profiles`
--

LOCK TABLES `cat_profiles` WRITE;
/*!40000 ALTER TABLE `cat_profiles` DISABLE KEYS */;
/*!40000 ALTER TABLE `cat_profiles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cat_relations`
--

DROP TABLE IF EXISTS `cat_relations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cat_relations` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `cat_id` bigint DEFAULT NULL,
  `related_id` bigint DEFAULT NULL,
  `type` tinyint DEFAULT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_cats` (`cat_id`,`related_id`),
  KEY `related_id` (`related_id`),
  CONSTRAINT `cat_relations_ibfk_1` FOREIGN KEY (`cat_id`) REFERENCES `cats` (`cat_id`),
  CONSTRAINT `cat_relations_ibfk_2` FOREIGN KEY (`related_id`) REFERENCES `cats` (`cat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cat_relations`
--

LOCK TABLES `cat_relations` WRITE;
/*!40000 ALTER TABLE `cat_relations` DISABLE KEYS */;
/*!40000 ALTER TABLE `cat_relations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cats`
--

DROP TABLE IF EXISTS `cats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cats` (
  `cat_id` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  `name` varchar(64) NOT NULL,
  `birthday` date DEFAULT NULL,
  `gender` tinyint DEFAULT '0',
  `breed` varchar(32) DEFAULT NULL,
  `color` varchar(32) DEFAULT NULL,
  `status` tinyint DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`cat_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `cats_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cats`
--

LOCK TABLES `cats` WRITE;
/*!40000 ALTER TABLE `cats` DISABLE KEYS */;
/*!40000 ALTER TABLE `cats` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `device_configs`
--

DROP TABLE IF EXISTS `device_configs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_configs` (
  `device_id` varchar(32) NOT NULL,
  `video_quality` varchar(16) DEFAULT NULL,
  `resolution` varchar(16) DEFAULT NULL,
  `record_mode` tinyint DEFAULT NULL,
  `motion_sensitivity` int DEFAULT NULL,
  `light_threshold` int DEFAULT NULL,
  `night_mode` tinyint(1) DEFAULT '1',
  `audio_enabled` tinyint(1) DEFAULT '1',
  `config_data` text,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_id`),
  CONSTRAINT `device_configs_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `device_configs`
--

LOCK TABLES `device_configs` WRITE;
/*!40000 ALTER TABLE `device_configs` DISABLE KEYS */;
/*!40000 ALTER TABLE `device_configs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `device_logs`
--

DROP TABLE IF EXISTS `device_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `device_id` varchar(32) DEFAULT NULL,
  `type` varchar(32) DEFAULT NULL,
  `level` tinyint DEFAULT NULL,
  `message` text,
  `data` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  CONSTRAINT `device_logs_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `device_logs`
--

LOCK TABLES `device_logs` WRITE;
/*!40000 ALTER TABLE `device_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `device_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `device_maintenance`
--

DROP TABLE IF EXISTS `device_maintenance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_maintenance` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `device_id` varchar(32) DEFAULT NULL,
  `type` varchar(32) DEFAULT NULL,
  `status` tinyint DEFAULT '1',
  `description` text,
  `scheduled_time` timestamp NULL DEFAULT NULL,
  `completed_time` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  CONSTRAINT `device_maintenance_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `device_maintenance`
--

LOCK TABLES `device_maintenance` WRITE;
/*!40000 ALTER TABLE `device_maintenance` DISABLE KEYS */;
/*!40000 ALTER TABLE `device_maintenance` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `device_shares`
--

DROP TABLE IF EXISTS `device_shares`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_shares` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `device_id` varchar(32) DEFAULT NULL,
  `user_id` bigint DEFAULT NULL,
  `sharer_id` bigint DEFAULT NULL,
  `permissions` text,
  `expire_at` timestamp NULL DEFAULT NULL,
  `status` tinyint DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_device_user` (`device_id`,`user_id`),
  KEY `user_id` (`user_id`),
  KEY `sharer_id` (`sharer_id`),
  CONSTRAINT `device_shares_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`),
  CONSTRAINT `device_shares_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `device_shares_ibfk_3` FOREIGN KEY (`sharer_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `device_shares`
--

LOCK TABLES `device_shares` WRITE;
/*!40000 ALTER TABLE `device_shares` DISABLE KEYS */;
/*!40000 ALTER TABLE `device_shares` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `device_status`
--

DROP TABLE IF EXISTS `device_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_status` (
  `device_id` varchar(32) NOT NULL,
  `power_status` tinyint DEFAULT NULL,
  `battery_level` int DEFAULT NULL,
  `temperature` decimal(4,1) DEFAULT NULL,
  `storage_used` bigint DEFAULT NULL,
  `storage_total` bigint DEFAULT NULL,
  `network_type` varchar(16) DEFAULT NULL,
  `signal_strength` int DEFAULT NULL,
  `ip_address` varchar(39) DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_id`),
  CONSTRAINT `device_status_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `device_status`
--

LOCK TABLES `device_status` WRITE;
/*!40000 ALTER TABLE `device_status` DISABLE KEYS */;
/*!40000 ALTER TABLE `device_status` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `devices`
--

DROP TABLE IF EXISTS `devices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `devices` (
  `device_id` varchar(32) NOT NULL,
  `user_id` bigint NOT NULL,
  `hardware_sn` varchar(64) NOT NULL,
  `name` varchar(64) DEFAULT NULL,
  `model` varchar(32) NOT NULL,
  `firmware_version` varchar(32) DEFAULT NULL,
  `status` tinyint DEFAULT '1',
  `last_heartbeat` timestamp NULL DEFAULT NULL,
  `last_active` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_id`),
  UNIQUE KEY `idx_hardware_sn` (`hardware_sn`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `devices_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `devices`
--

LOCK TABLES `devices` WRITE;
/*!40000 ALTER TABLE `devices` DISABLE KEYS */;
INSERT INTO `devices` VALUES ('20250226021fb25a9f021000',152249060978462720,'12345678','客厅猫厕所','CT-2024-Pro','v1.0.0',1,NULL,NULL,'2025-02-26 07:14:28','2025-02-26 07:14:28');
/*!40000 ALTER TABLE `devices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notifications` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL,
  `type` varchar(32) DEFAULT NULL,
  `title` varchar(128) DEFAULT NULL,
  `content` text,
  `level` tinyint DEFAULT '1',
  `is_read` tinyint(1) DEFAULT '0',
  `read_time` timestamp NULL DEFAULT NULL,
  `extra_data` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications`
--

LOCK TABLES `notifications` WRITE;
/*!40000 ALTER TABLE `notifications` DISABLE KEYS */;
/*!40000 ALTER TABLE `notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_hardware`
--

DROP TABLE IF EXISTS `user_hardware`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_hardware` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `hardware_sn` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '硬件序列号',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-正常 2-禁用',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_hardware_sn` (`hardware_sn`),
  KEY `idx_user_hw` (`user_id`),
  CONSTRAINT `fk_user_hardware_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户硬件关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_hardware`
--

LOCK TABLES `user_hardware` WRITE;
/*!40000 ALTER TABLE `user_hardware` DISABLE KEYS */;
INSERT INTO `user_hardware` VALUES (2,152249060978462720,'12345678',1,'客厅设备','2025-02-24 11:58:42','2025-02-26 05:29:27');
/*!40000 ALTER TABLE `user_hardware` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_logs`
--

DROP TABLE IF EXISTS `user_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL,
  `type` varchar(32) DEFAULT NULL,
  `ip` varchar(39) DEFAULT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `description` text,
  `data` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `user_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_logs`
--

LOCK TABLES `user_logs` WRITE;
/*!40000 ALTER TABLE `user_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_profiles`
--

DROP TABLE IF EXISTS `user_profiles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_profiles` (
  `user_id` bigint NOT NULL,
  `nickname` varchar(64) DEFAULT NULL,
  `real_name` varchar(64) DEFAULT NULL,
  `gender` tinyint DEFAULT '0',
  `birthday` date DEFAULT NULL,
  `avatar_url` varchar(255) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `city` varchar(64) DEFAULT NULL,
  `country` varchar(64) DEFAULT NULL,
  `emerg_contact` varchar(64) DEFAULT NULL,
  `emerg_phone` varchar(20) DEFAULT NULL,
  `is_verified` tinyint(1) DEFAULT '0',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`),
  CONSTRAINT `user_profiles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_profiles`
--

LOCK TABLES `user_profiles` WRITE;
/*!40000 ALTER TABLE `user_profiles` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_profiles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_relations`
--

DROP TABLE IF EXISTS `user_relations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_relations` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL,
  `related_id` bigint DEFAULT NULL,
  `type` tinyint DEFAULT NULL,
  `role` tinyint DEFAULT NULL,
  `permissions` text,
  `status` tinyint DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_users` (`user_id`,`related_id`),
  KEY `related_id` (`related_id`),
  CONSTRAINT `user_relations_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `user_relations_ibfk_2` FOREIGN KEY (`related_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_relations`
--

LOCK TABLES `user_relations` WRITE;
/*!40000 ALTER TABLE `user_relations` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_relations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_settings`
--

DROP TABLE IF EXISTS `user_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_settings` (
  `user_id` bigint NOT NULL,
  `language` varchar(8) DEFAULT 'zh',
  `time_zone` varchar(32) DEFAULT NULL,
  `notification` tinyint(1) DEFAULT '1',
  `newsletter` tinyint(1) DEFAULT '1',
  `theme` varchar(16) DEFAULT NULL,
  `settings` text,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`),
  CONSTRAINT `user_settings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_settings`
--

LOCK TABLES `user_settings` WRITE;
/*!40000 ALTER TABLE `user_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `user_id` bigint NOT NULL,
  `username` varchar(64) NOT NULL,
  `password_hash` varchar(64) NOT NULL,
  `email` varchar(128) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `nickname` varchar(64) DEFAULT NULL,
  `status` tinyint DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `idx_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (152249060978462720,'test_usder','85777f270ad7cf2a790981bbae3c4e484a1dc55e24a77390d692fbf1cffa12fa','<EMAIL>','13800138000','测试用户',1,'2025-02-24 03:03:25','2025-02-24 03:03:25',NULL);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `video_analysis`
--

DROP TABLE IF EXISTS `video_analysis`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `video_analysis` (
  `video_id` bigint NOT NULL,
  `cat_id` bigint DEFAULT NULL,
  `cat_confidence` decimal(5,4) DEFAULT NULL,
  `face_match` decimal(5,4) DEFAULT NULL,
  `body_match` decimal(5,4) DEFAULT NULL,
  `pose_data` text,
  `behavior_type` tinyint DEFAULT NULL,
  `behavior_data` text,
  `activity_level` decimal(3,2) DEFAULT NULL,
  `is_abnormal` tinyint(1) DEFAULT '0',
  `abnormal_type` tinyint DEFAULT NULL,
  `abnormal_prob` decimal(5,4) DEFAULT NULL,
  `ai_results` text,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`video_id`),
  KEY `idx_cat_id` (`cat_id`),
  KEY `idx_is_abnormal` (`is_abnormal`),
  CONSTRAINT `video_analysis_ibfk_1` FOREIGN KEY (`video_id`) REFERENCES `video_records` (`video_id`),
  CONSTRAINT `video_analysis_ibfk_2` FOREIGN KEY (`cat_id`) REFERENCES `cats` (`cat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `video_analysis`
--

LOCK TABLES `video_analysis` WRITE;
/*!40000 ALTER TABLE `video_analysis` DISABLE KEYS */;
/*!40000 ALTER TABLE `video_analysis` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `video_records`
--

DROP TABLE IF EXISTS `video_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `video_records` (
  `video_id` bigint NOT NULL,
  `device_id` varchar(32) NOT NULL,
  `start_time` timestamp NOT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `duration` int DEFAULT NULL,
  `video_path` varchar(255) DEFAULT NULL,
  `file_size` bigint DEFAULT NULL,
  `resolution` varchar(16) DEFAULT NULL,
  `format` varchar(8) DEFAULT NULL,
  `framerate` int DEFAULT NULL,
  `has_audio` tinyint(1) DEFAULT NULL,
  `status` tinyint DEFAULT '1',
  `process_stage` tinyint DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `entry_weight` decimal(5,2) DEFAULT NULL,
  `exit_weight` decimal(5,2) DEFAULT NULL,
  `waste_weight` decimal(5,2) DEFAULT NULL,
  PRIMARY KEY (`video_id`),
  UNIQUE KEY `idx_device_time` (`device_id`,`start_time`),
  CONSTRAINT `video_records_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `video_records`
--

LOCK TABLES `video_records` WRITE;
/*!40000 ALTER TABLE `video_records` DISABLE KEYS */;
/*!40000 ALTER TABLE `video_records` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `video_sensor_data`
--

DROP TABLE IF EXISTS `video_sensor_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `video_sensor_data` (
  `video_id` bigint NOT NULL,
  `entry_weight` decimal(5,2) DEFAULT NULL,
  `exit_weight` decimal(5,2) DEFAULT NULL,
  `waste_weight` decimal(5,3) DEFAULT NULL,
  `weight_curve` text,
  `accel_data` text,
  `motion_pattern` text,
  `vibration` decimal(5,2) DEFAULT NULL,
  `temperature` decimal(4,1) DEFAULT NULL,
  `humidity` decimal(4,1) DEFAULT NULL,
  `brightness` int DEFAULT NULL,
  `sensor_data` text,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`video_id`),
  CONSTRAINT `video_sensor_data_ibfk_1` FOREIGN KEY (`video_id`) REFERENCES `video_records` (`video_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `video_sensor_data`
--

LOCK TABLES `video_sensor_data` WRITE;
/*!40000 ALTER TABLE `video_sensor_data` DISABLE KEYS */;
/*!40000 ALTER TABLE `video_sensor_data` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `video_tags`
--

DROP TABLE IF EXISTS `video_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `video_tags` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `video_id` bigint DEFAULT NULL,
  `type` varchar(32) DEFAULT NULL,
  `name` varchar(64) DEFAULT NULL,
  `confidence` decimal(5,4) DEFAULT NULL,
  `start_time` double DEFAULT NULL,
  `duration` double DEFAULT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_video_id` (`video_id`),
  CONSTRAINT `video_tags_ibfk_1` FOREIGN KEY (`video_id`) REFERENCES `video_records` (`video_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `video_tags`
--

LOCK TABLES `video_tags` WRITE;
/*!40000 ALTER TABLE `video_tags` DISABLE KEYS */;
/*!40000 ALTER TABLE `video_tags` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-02-26 15:55:25
