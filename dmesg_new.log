root@aby-box-arm:~# dmesg
[    0.000000] Booting Linux on physical CPU 0x0000000000 [0x410fd034]
[    0.000000] Linux version 5.10.4-aby (oe-user@oe-host) (aarch64-oe-linux-gcc (GCC) 14.2.0, GNU ld (GNU Binutils) 2.43.1) #1 SMP PREEMPT Thu Jun 12 10:21:29 UTC 2025
[    0.000000] Machine model: Aby Box
[    0.000000] efi: UEFI not found.
[    0.000000] Ion: Ion memory setup at 0x0000000095400000 size 170 MiB
[    0.000000] OF: reserved mem: initialized node ion, compatible id ion-region
[    0.000000] Zone ranges:
[    0.000000]   DMA      [mem 0x0000000080000000-0x000000009fdfffff]
[    0.000000]   DMA32    empty
[    0.000000]   Normal   empty
[    0.000000] Movable zone start for each node
[    0.000000] Early memory node ranges
[    0.000000]   node   0: [mem 0x0000000080000000-0x0000000094c2ffff]
[    0.000000]   node   0: [mem 0x0000000095400000-0x000000009fdfffff]
[    0.000000] Zeroed struct page in unavailable ranges: 1488 pages
[    0.000000] Initmem setup node 0 [mem 0x0000000080000000-0x000000009fdfffff]
[    0.000000] On node 0 totalpages: 128560
[    0.000000]   DMA zone: 2040 pages used for memmap
[    0.000000]   DMA zone: 0 pages reserved
[    0.000000]   DMA zone: 128560 pages, LIFO batch:31
[    0.000000] psci: probing for conduit method from DT.
[    0.000000] psci: PSCIv1.0 detected in firmware.
[    0.000000] psci: Using standard PSCI v0.2 function IDs
[    0.000000] psci: MIGRATE_INFO_TYPE not supported.
[    0.000000] psci: SMC Calling Convention v1.0
[    0.000000] percpu: Embedded 20 pages/cpu s44824 r8192 d28904 u81920
[    0.000000] pcpu-alloc: s44824 r8192 d28904 u81920 alloc=20*4096
[    0.000000] pcpu-alloc: [0] 0 
[    0.000000] Detected VIPT I-cache on CPU0
[    0.000000] CPU features: detected: ARM erratum 845719
[    0.000000] Built 1 zonelists, mobility grouping on.  Total pages: 126520
[    0.000000] Kernel command line: root=/dev/mmcblk0p3 console=ttyS0,115200 earlycon=sbi loglevel=9 rootwait rw
[    0.000000] Dentry cache hash table entries: 65536 (order: 7, 524288 bytes, linear)
[    0.000000] Inode-cache hash table entries: 32768 (order: 6, 262144 bytes, linear)
[    0.000000] mem auto-init: stack:off, heap alloc:off, heap free:off
[    0.000000] Memory: 310520K/514240K available (6208K kernel code, 618K rwdata, 1956K rodata, 1600K init, 247K bss, 203720K reserved, 0K cma-reserved)
[    0.000000] SLUB: HWalign=64, Order=0-3, MinObjects=0, CPUs=1, Nodes=1
[    0.000000] rcu: Preemptible hierarchical RCU implementation.
[    0.000000] rcu: 	RCU event tracing is enabled.
[    0.000000] rcu: 	RCU restricting CPUs from NR_CPUS=256 to nr_cpu_ids=1.
[    0.000000] 	Trampoline variant of Tasks RCU enabled.
[    0.000000] rcu: RCU calculated value of scheduler-enlistment delay is 25 jiffies.
[    0.000000] rcu: Adjusting geometry for rcu_fanout_leaf=16, nr_cpu_ids=1
[    0.000000] NR_IRQS: 64, nr_irqs: 64, preallocated irqs: 0
[    0.000000] arch_timer: cp15 timer(s) running at 25.00MHz (virt).
[    0.000000] clocksource: arch_sys_counter: mask: 0xffffffffffffff max_cycles: 0x5c40939b5, max_idle_ns: 440795202646 ns
[    0.000006] sched_clock: 56 bits at 25MHz, resolution 40ns, wraps every 4398046511100ns
[    0.000062] Calibrating delay loop (skipped), value calculated using timer frequency.. 50.00 BogoMIPS (lpj=100000)
[    0.000081] pid_max: default: 4096 minimum: 301
[    0.000227] Mount-cache hash table entries: 1024 (order: 1, 8192 bytes, linear)
[    0.000243] Mountpoint-cache hash table entries: 1024 (order: 1, 8192 bytes, linear)
[    0.001628] rcu: Hierarchical SRCU implementation.
[    0.002338] EFI services will not be available.
[    0.002550] smp: Bringing up secondary CPUs ...
[    0.002565] smp: Brought up 1 node, 1 CPU
[    0.002573] SMP: Total of 1 processors activated.
[    0.002589] CPU features: detected: 32-bit EL0 Support
[    0.002602] CPU features: detected: CRC32 instructions
[    0.002666] CPU: All CPU(s) started at EL1
[    0.002686] alternatives: patching kernel code
[    0.003331] devtmpfs: initialized
[    0.007733] early_time_log: do_initcalls: 4933410us
[    0.007915] random: get_random_u32 called from bucket_table_alloc.isra.0+0xf4/0x120 with crng_init=0
[    0.008392] clocksource: jiffies: mask: 0xffffffff max_cycles: 0xffffffff, max_idle_ns: 7645041785100000 ns
[    0.008422] futex hash table entries: 16 (order: -2, 1024 bytes, linear)
[    0.008513] pinctrl core: initialized pinctrl subsystem
[    0.008721] DMI not present or invalid.
[    0.008919] NET: Registered protocol family 16
[    0.009519] DMA: preallocated 128 KiB GFP_KERNEL pool for atomic allocations
[    0.009567] DMA: preallocated 128 KiB GFP_KERNEL|GFP_DMA pool for atomic allocations
[    0.009608] DMA: preallocated 128 KiB GFP_KERNEL|GFP_DMA32 pool for atomic allocations
[    0.010055] thermal_sys: Registered thermal governor 'step_wise'
[    0.010537] ASID allocator initialised with 65536 entries
[    0.020746] OF: /gpio@03020000/gpio-controller@0: could not find phandle
[    0.020819] OF: /gpio@03021000/gpio-controller@1: could not find phandle
[    0.020881] OF: /gpio@03022000/gpio-controller@2: could not find phandle
[    0.020939] OF: /gpio@03023000/gpio-controller@3: could not find phandle
[    0.020997] OF: /gpio@05021000/gpio-controller@4: could not find phandle
[    0.022847] clk reset: nr_reset=64 resource_size=8
[    0.023363] get audio clk=24576000
[    0.023422] cvitek-i2s-subsys 4108000.i2s_subsys: Set clk_sdma_aud0~3 to 24576000
[    0.034053] dw_dmac 4330000.dma: CVITEK DMA Controller, 8 channels, probe done!
[    0.034723] SCSI subsystem initialized
[    0.034944] usbcore: registered new interface driver usbfs
[    0.035000] usbcore: registered new interface driver hub
[    0.035100] usbcore: registered new device driver usb
[    0.038008] Ion: ion_parse_dt_heap_common: id 0 type 2 name carveout align 1000
[    0.038485] Ion: rmem_ion_device_init: heap carveout base 0x0000000095400000 size 0x000000000aa00000 dev (____ptrval____)
[    0.038500] ion_carveout_heap_create, size=0xaa00000
[    0.038628] cvi_get_rtos_ion_size, rtos ion_size get:0x1600000
[    0.038637] ion_carveout_heap_create, size(exclusion of rtos_ion_size)=0x9400000
[    0.091875] platform carveout: [ion] add heap id 0, type 2, base 0x95400000, size 0xaa00000
[    0.092253] Advanced Linux Sound Architecture Driver Initialized.
[    0.092873] Bluetooth: Core ver 2.22
[    0.092942] NET: Registered protocol family 31
[    0.092952] Bluetooth: HCI device and connection manager initialized
[    0.092971] Bluetooth: HCI socket layer initialized
[    0.092983] Bluetooth: L2CAP socket layer initialized
[    0.093005] Bluetooth: SCO socket layer initialized
[    0.093544] clocksource: Switched to clocksource arch_sys_counter
[    0.094958] NET: Registered protocol family 2
[    0.095705] tcp_listen_portaddr_hash hash table entries: 256 (order: 0, 4096 bytes, linear)
[    0.095736] TCP established hash table entries: 4096 (order: 3, 32768 bytes, linear)
[    0.095788] TCP bind hash table entries: 4096 (order: 4, 65536 bytes, linear)
[    0.095868] TCP: Hash tables configured (established 4096 bind 4096)
[    0.095984] UDP hash table entries: 256 (order: 1, 8192 bytes, linear)
[    0.096015] UDP-Lite hash table entries: 256 (order: 1, 8192 bytes, linear)
[    0.096167] NET: Registered protocol family 1
[    0.096933] RPC: Registered named UNIX socket transport module.
[    0.096953] RPC: Registered udp transport module.
[    0.096961] RPC: Registered tcp transport module.
[    0.096969] RPC: Registered tcp NFSv4.1 backchannel transport module.
[    0.098966] Initialise system trusted keyrings
[    0.099199] workingset: timestamp_bits=62 max_order=17 bucket_order=0
[    0.104083] squashfs: version 4.0 (2009/01/31) Phillip Lougher
[    0.158125] Key type asymmetric registered
[    0.158146] Asymmetric key parser 'x509' registered
[    0.158689] cvitek,pinctrl-cv181x 3001000.pinctrl: cvi_pinctrl_probe(): reg=(____ptrval____),4096 CVITEK_PINMUX_REG_LAST=0x1cc
[    0.163747] Serial: 8250/16550 driver, 5 ports, IRQ sharing disabled
[    0.165127] printk: console [ttyS0] disabled
[    0.165223] 4140000.serial: ttyS0 at MMIO 0x4140000 (irq = 23, base_baud = 1562500) is a 16550A
[    0.914009] printk: console [ttyS0] enabled
[    0.919079] 4150000.serial: ttyS1 at MMIO 0x4150000 (irq = 24, base_baud = 1562500) is a 16550A
[    0.928886] 4170000.serial: ttyS3 at MMIO 0x4170000 (irq = 25, base_baud = 1562500) is a 16550A
[    0.938678] 41c0000.serial: ttyS4 at MMIO 0x41c0000 (irq = 26, base_baud = 1562500) is a 16550A
[    0.954706] loop: module loaded
[    0.958558] zram: Added device: zram0
[    0.963421] cvi-spif 10000000.cvi-spif: unrecognized JEDEC id bytes: ff ff ff ff ff ff
[    0.971638] cvi-spif 10000000.cvi-spif: device scan failed
[    0.977315] cvi-spif 10000000.cvi-spif: unable to setup flash chip
[    0.985024] libphy: Fixed MDIO Bus: probed
[    0.989971] bm-dwmac 4070000.ethernet: IRQ eth_wake_irq not found
[    0.996311] bm-dwmac 4070000.ethernet: IRQ eth_lpi not found
[    1.002254] bm-dwmac 4070000.ethernet: Hash table entries set to unexpected value 0
[    1.010307] bm-dwmac 4070000.ethernet: no reset control found
[    1.016466] bm-dwmac 4070000.ethernet: User ID: 0x10, Synopsys ID: 0x37
[    1.023345] bm-dwmac 4070000.ethernet: 	DWMAC1000
[    1.028217] bm-dwmac 4070000.ethernet: DMA HW capability register supported
[    1.035417] bm-dwmac 4070000.ethernet: RX Checksum Offload Engine supported
[    1.042616] bm-dwmac 4070000.ethernet: COE Type 2
[    1.047483] bm-dwmac 4070000.ethernet: TX Checksum insertion supported
[    1.054233] bm-dwmac 4070000.ethernet: Normal descriptors
[    1.059856] bm-dwmac 4070000.ethernet: Ring mode enabled
[    1.065353] bm-dwmac 4070000.ethernet: Enable RX Mitigation via HW Watchdog Timer
[    1.073097] bm-dwmac 4070000.ethernet: device MAC address 4e:8c:cd:02:f1:79
[    1.103125] libphy: stmmac: probed
[    1.108033] bm-dwmac 4070000.ethernet: Cannot get clk_500m_eth!
[    1.114417] bm-dwmac 4070000.ethernet: Cannot get gate_clk_axi4!
[    1.120952] usbcore: registered new interface driver asix
[    1.126662] usbcore: registered new interface driver ax88179_178a
[    1.133032] usbcore: registered new interface driver cdc_ether
[    1.139126] usbcore: registered new interface driver smsc95xx
[    1.145102] usbcore: registered new interface driver net1080
[    1.150988] usbcore: registered new interface driver cdc_subset
[    1.157150] usbcore: registered new interface driver zaurus
[    1.162964] usbcore: registered new interface driver cdc_ncm
[    1.169708] dwc2 4340000.usb: axi clk installed
[    1.174421] dwc2 4340000.usb: apb clk installed
[    1.179114] dwc2 4340000.usb: 125m clk installed
[    1.183896] dwc2 4340000.usb: 33k clk installed
[    1.188586] dwc2 4340000.usb: 12m clk installed
[    1.193345] dwc2 4340000.usb: EPs: 8, dedicated fifos, 3072 entries in SPRAM
[    1.200932] dwc2 4340000.usb: DWC OTG Controller
[    1.205759] dwc2 4340000.usb: new USB bus registered, assigned bus number 1
[    1.212988] dwc2 4340000.usb: irq 48, io mem 0x04340000
[    1.219165] hub 1-0:1.0: USB hub found
[    1.223164] hub 1-0:1.0: 1 port detected
[    1.228618] usbcore: registered new interface driver uas
[    1.234281] usbcore: registered new interface driver usb-storage
[    1.240836] i2c /dev entries driver
[    1.245753] Bluetooth: HCI UART driver ver 2.3
[    1.250386] Bluetooth: HCI UART protocol H4 registered
[    1.256104] sdhci: Secure Digital Host Controller Interface driver
[    1.262514] sdhci: Copyright(c) Pierre Ossman
[    1.267023] sdhci-pltfm: SDHCI platform and OF driver helper
[    1.273085] cvi:sdhci_cvi_probe
[    1.321542] mmc0: SDHCI controller on 4310000.cv-sd [4310000.cv-sd] using ADMA 64-bit
[    1.329907] cvi_proc_init cvi_host 0x(____ptrval____)
[    1.335345] cvi:sdhci_cvi_probe
[    1.347792] mmc1 bounce up to 128 segments into one, max segment size 65536 bytes
[    1.386086] mmc1: SDHCI controller on 4320000.wifi-sd [4320000.wifi-sd] using DMA
[    1.394530] usbcore: registered new interface driver usbhid
[    1.400483] usbhid: USB HID core driver
[    1.405222] usbcore: registered new interface driver snd-usb-audio
[    1.412548] cvitek-i2s 4100000.i2s: cvi_i2s_probe
[    1.418196] cvitek-i2s 4110000.i2s: cvi_i2s_probe
[    1.423806] cvitek-i2s 4120000.i2s: cvi_i2s_probe
[    1.434120] cvitek-i2s 4130000.i2s: cvi_i2s_probe
[    1.440928] cv1835-pdm sound_PDM: cv1835_pdm_probe, dev name=sound_PDM
[    1.448186] cv1835pdm 41d0c00.pdm: cv1835pdm_probe
[    1.458131] cviteka-adc sound_adc: cviteka_adc_probe, dev name=sound_adc
[    1.465242] cviteka-adc sound_adc: cviteka_adc_probe start devm_snd_soc_register_card
[    1.477649] cvitekaadc 300a100.adc: cvitekaadc_probe
[    1.483683] cviteka-dac sound_dac: cviteka_dac_probe, dev name=sound_dac
[    1.491177] cvitekadac 300a000.dac: cvitekadac_probe
[    1.501069] cvitekadac_probe gpio_is_valid mute_pin_l
[    1.513534] cvitekadac_probe gpio_is_valid mute_pin_r
[    1.519741] NET: Registered protocol family 10
[    1.527017] Segment Routing with IPv6
[    1.531101] sit: IPv6, IPv4 and MPLS over IPv4 tunneling driver
[    1.540259] NET: Registered protocol family 17
[    1.545302] Bluetooth: RFCOMM TTY layer initialized
[    1.550611] Bluetooth: RFCOMM socket layer initialized
[    1.556196] Bluetooth: RFCOMM ver 1.11
[    1.560251] Bluetooth: BNEP (Ethernet Emulation) ver 1.3
[    1.565937] Bluetooth: BNEP filters: protocol multicast
[    1.571578] Bluetooth: BNEP socket layer initialized
[    1.576787] Bluetooth: HIDP (Human Interface Emulation) ver 1.2
[    1.582974] Bluetooth: HIDP socket layer initialized
[    1.588586] Loading compiled-in X.509 certificates
[    1.597386] random: fast init done
[    1.635927] cv1835-pdm sound_PDM: cv1835_pdm_probe, dev name=sound_PDM
[    1.643253] cv1835pdm_component_probe
[    1.648840] cv1835-pdm sound_PDM: ASoC: no DMI vendor name!
[    1.656026] cviteka-adc sound_adc: cviteka_adc_probe, dev name=sound_adc
[    1.663074] cviteka-adc sound_adc: cviteka_adc_probe start devm_snd_soc_register_card
[    1.671762] cviteka-adc sound_adc: ASoC: no DMI vendor name!
[    1.679099] cviteka-dac sound_dac: cviteka_dac_probe, dev name=sound_dac
[    1.688288] cviteka-dac sound_dac: ASoC: no DMI vendor name!
[    1.696126] cfg80211: Loading compiled-in X.509 certificates for regulatory database
[    1.707863] cfg80211: Loaded X.509 cert 'sforshee: 00b28ddf47aef9cea7'
[    1.714945] cfg80211: failed to load regulatory.db
[    1.720345] ALSA device list:
[    1.724159] dw-apb-uart 4140000.serial: forbid DMA for kernel console
[    1.731096] Waiting for root device /dev/mmcblk0p3...
[    2.261339] mmc0: new ultra high speed SDR104 SDXC card at address aaaa
[    2.268875] mmcblk0: mmc0:aaaa SP64G 59.5 GiB 
[    2.278373]  mmcblk0: p1 p2 p3 p4
[    2.357800] random: crng init done
[    3.048053] EXT4-fs (mmcblk0p3): recovery complete
[    3.058011] EXT4-fs (mmcblk0p3): mounted filesystem with ordered data mode. Opts: (null)
[    3.066480] VFS: Mounted root (ext4 filesystem) on device 179:3.
[    3.075251] devtmpfs: mounted
[    3.079424] Freeing unused kernel memory: 1600K
[    3.084292] Run /sbin/init as init process
[    3.088544]   with arguments:
[    3.091621]     /sbin/init
[    3.094426]   with environment:
[    3.097679]     HOME=/
[    3.100117]     TERM=linux
[    3.102923] early_time_log: run_init_process: 8028609us
[    4.948944] g_ether: bad vermagic: kernel tainted.
[    4.954033] Disabling lock debugging due to kernel taint
[    4.960441] using random self ethernet address
[    4.965321] using random host ethernet address
[    4.970771] usb0: HOST MAC 22:76:25:93:54:98
[    4.975584] usb0: MAC 72:28:c5:95:34:42
[    4.979795] using random self ethernet address
[    4.984559] using random host ethernet address
[    4.989381] g_ether gadget: Ethernet Gadget, version: Memorial Day 2008
[    4.996410] g_ether gadget: g_ether ready
[    5.000702] dwc2 4340000.usb: bound driver g_ether
[    5.158552] bm-dwmac 4070000.ethernet eth0: validation of rmii with support 0000000,00000000,00006280 and advertisement 0000000,00000000,00006280 failed: -22
[    5.173515] bm-dwmac 4070000.ethernet eth0: stmmac_open: Cannot attach to PHY (error: -22)
[    5.204913] dwc2 4340000.usb: new device is high-speed
[    5.315674] CVITEK CHIP ID = 15
[    5.319153] dwc2 4340000.usb: new device is high-speed
[    5.346610] cvi_rtos_cmdqu_probe start ---
[    5.350905] name=1900000.rtos_cmdqu
[    5.354991] res-reg: start: 0x1900000, end: 0x1900fff, virt-addr(ffffffc010c45000).
[    5.363232] cvi_rtos_cmdqu_probe DONE
[    5.367462] [cvi_spinlock_init] success
[    5.378108] dwc2 4340000.usb: new address 5
[    5.464069] IPv6: ADDRCONF(NETDEV_CHANGE): usb0: link becomes ready
[    5.601562] RTOS_CMDQU_SEND_WAIT timeout
[    5.605646] SYS_CMD_INFO_LINUX_INIT_DONE fail
[    5.610422] communicate with rtos fail
[    5.647261] cif a0c2000.cif: cam0 clk installed
[    5.652013] cif a0c2000.cif: cam1 clk installed
[    5.657000] cif a0c2000.cif: vip_sys_2 clk installed
[    5.662336] cif a0c2000.cif: clk_mipimpll clk installed 0000000002ea19e2
[    5.669494] cif a0c2000.cif: clk_disppll clk installed 000000000dab561b
[    5.676538] cif a0c2000.cif: clk_fpll clk installed 0000000038f9b7fb
[    5.683286] cif a0c2000.cif: (0) res-reg: start: 0xa0c2000, end: 0xa0c3fff.
[    5.690627] cif a0c2000.cif:  virt-addr(000000005982b87e)
[    5.696358] cif a0c2000.cif: (1) res-reg: start: 0xa0d0000, end: 0xa0d0fff.
[    5.703693] cif a0c2000.cif:  virt-addr(00000000cc8b820a)
[    5.709425] cif a0c2000.cif: (2) res-reg: start: 0xa0c4000, end: 0xa0c5fff.
[    5.716757] cif a0c2000.cif:  virt-addr(00000000de0430ce)
[    5.722487] cif a0c2000.cif: (3) res-reg: start: 0xa0c6000, end: 0xa0c7fff.
[    5.729826] cif a0c2000.cif:  virt-addr(00000000a1b8da4c)
[    5.735558] cif a0c2000.cif: (4) res-reg: start: 0x3001c30, end: 0x3001c5f.
[    5.742897] cif a0c2000.cif:  virt-addr(00000000ec624315)
[    5.748735] cif a0c2000.cif: no pad_ctrl for cif
[    5.753762] cif a0c2000.cif: request irq-37 as cif-irq0
[    5.759439] cif a0c2000.cif: request irq-38 as cif-irq1
[    5.765199] cif a0c2000.cif: rst_pin = 482, pol = 1
[    5.786548] snsr_i2c snsr_i2c: i2c:-------hook 1
[    5.791479] snsr_i2c snsr_i2c: i2c:-------hook 2
[    5.796652] snsr_i2c snsr_i2c: i2c:-------hook 3
[    5.914150] vi_core_probe:193(): res-reg: start: 0xa000000, end: 0xa07ffff, virt-addr(ffffffc010f00000).
[    5.924027] vi_core_probe:206(): irq(39) for isp get from platform driver.
[    5.931951] sync_task_init:177(): sync_task_init vi_pipe 0
[    5.937877] sync_task_init:177(): sync_task_init vi_pipe 1
[    5.943882] sync_task_init:177(): sync_task_init vi_pipe 2
[    5.949707] sync_task_init:177(): sync_task_init vi_pipe 3
[    5.955499] sync_task_init:177(): sync_task_init vi_pipe 4
[    5.961738] vi_core_probe:242(): isp registered as cvi-vi
[    6.088208] vpss_start_handler:5134(): handler for dev(0) started
[    6.088331] vpss_start_handler:5134(): handler for dev(1) started
[    6.182693] cvi-mipi-tx mipi_tx: IRQ index 0 not found
[    6.194460] cvi-mipi-tx mipi_tx: vbat irq(-6)
[    6.199291] cvi-mipi-tx mipi_tx: reset gpio pin(354) active(0)
[    6.205484] cvi-mipi-tx mipi_tx: power ctrl gpio pin(353) active(1)
[    6.212097] cvi-mipi-tx mipi_tx: pwm gpio pin(352) active(1)
[    6.280361] cv181x-cooling cv181x_cooling: elems of dev-freqs=6
[    6.286627] cv181x-cooling cv181x_cooling: dev_freqs[0]: 800000000 500000000
[    6.294193] cv181x-cooling cv181x_cooling: dev_freqs[1]: 400000000 375000000
[    6.301654] cv181x-cooling cv181x_cooling: dev_freqs[2]: 400000000 300000000
[    6.309179] cv181x-cooling cv181x_cooling: Cooling device registered: cv181x_cooling
[    6.365674] [INFO] Register SBM IRQ ###################################
[    6.365701] [INFO] pvctx->s_sbm_irq = 45
[    6.391268] jpu ctrl reg pa = 0xb030000, va = 00000000ab4927f9, size = 256
[    6.402818] end jpu_init result = 0x0
[    6.581126] cvi_vc_drv_init result = 0x0
[    6.653355] pstStCandiCornerCtrl->stMem.u32Size must be greater than or equal to 16400!
[    7.713590] i2c_designware 4010000.i2c: controller timed out
[    7.719482] ltr303 1-0029: can't get Part ID: -110
[    7.724808] ltr303: probe of 1-0029 failed with error -110
[    7.771034] aicbsp_init
[    7.773628] RELEASE_DATE:2024_0327_3561b08f
[    7.778321] aicbsp_resv_mem_init 
[    7.927140] aicbsp: aicbsp_set_subsys, subsys: AIC_WIFI, state to: 1
[    7.933768] aicbsp: aicbsp_set_subsys, power state change to 1 dure to AIC_WIFI
[    7.941632] aicbsp: aicbsp_platform_power_on
[    7.946190] ======== CVITEK WLAN_POWER_ON ========
[    8.001354] aicbsp_platform_power_on,539: cvi_sdio_rescan
[    8.053865] mmc1: new high speed SDIO card at address 390b
[    8.075320] aicbsp: aicbsp_sdio_probe:1 vid:0xC8A1  did:0x0082
[    8.081481] aicbsp: aicbsp_sdio_probe:2 vid:0xC8A1  did:0x0182
[    8.087786] aicbsp: aicbsp_sdio_probe after replace:1
[    8.093182] aicbsp: aicbsp_get_feature, set FEATURE_SDIO_CLOCK 150 MHz
[    8.100078] aicbsp: aicwf_sdio_reg_init
[    8.105066] rwnx_load_firmware :firmware path = /etc/firmware/aic8800/fw_patch_table_8800d80_u02.bin  
[    8.119123] file md5:0c9bf9c9c10f7a90a22a4c35fa58c967
[    8.125372] rwnx_plat_bin_fw_upload_android
[    8.130017] rwnx_load_firmware :firmware path = /etc/firmware/aic8800/fw_adid_8800d80_u02.bin  
[    8.140917] file md5:f546881a81b960d89a672578eb45a809
[    8.147606] rwnx_plat_bin_fw_upload_android
[    8.152190] rwnx_load_firmware :firmware path = /etc/firmware/aic8800/fw_patch_8800d80_u02.bin  
[    8.170794] file md5:35d137b8a76daaeb4f5034df8e15bcde
[    8.202000] aicbt_patch_table_load bt btmode[3]:5 
[    8.207075] aicbt_patch_table_load bt uart_baud[3]:1500000 
[    8.213179] aicbt_patch_table_load bt uart_flowctrl[3]:1 
[    8.219003] aicbt_patch_table_load bt lpm_enable[3]:0 
[    8.224533] aicbt_patch_table_load bt tx_pwr[3]:28463 
[    8.237791] aicbsp: bt patch version: - Mar 07 2024 14:29:05 - git f94a3e4
[    8.245032] rwnx_plat_bin_fw_upload_android
[    8.249645] rwnx_load_firmware :firmware path = /etc/firmware/aic8800/fmacfw_8800d80_u02.bin  
[    8.336996] file md5:13e6f0e58aae342d260d8672ab61c31f
[    8.435373] rd_version_val=06090101
[    8.455719] aicbsp: aicbsp_get_feature, set FEATURE_SDIO_CLOCK 150 MHz
[    8.462499] aicsdio: aicwf_sdio_reg_init
[    8.472030] aicbsp: aicbsp_resv_mem_alloc_skb, alloc resv_mem_txdata succuss, id: 0, size: 98304
[    8.481514] aicbsp: aicbsp_get_feature, set FEATURE_SDIO_CLOCK 150 MHz
[    8.489585] aicbsp: sdio_err:<aicwf_sdio_bus_pwrctl,1431>: bus down
[    8.499954] AICWFDBG(LOGERROR)	invalid cmd: lvl_adj_5g_chan_42
[    8.506036] AICWFDBG(LOGERROR)	invalid cmd: lvl_adj_5g_chan_58
[    8.512331] AICWFDBG(LOGERROR)	invalid cmd: lvl_adj_5g_chan_106
[    8.518622] AICWFDBG(LOGERROR)	invalid cmd: lvl_adj_5g_chan_122
[    8.524890] AICWFDBG(LOGERROR)	invalid cmd: lvl_adj_5g_chan_138
[    8.531250] AICWFDBG(LOGERROR)	invalid cmd: lvl_adj_5g_chan_155
[    9.224033] ieee80211 phy0: HT supp 1, VHT supp 1, HE supp 1
[    9.262694] rcS (148): drop_caches: 3
[   10.318483] EXT4-fs (mmcblk0p4): recovery complete
[   10.325029] EXT4-fs (mmcblk0p4): mounted filesystem with ordered data mode. Opts: (null)
[   12.529356] rwnx_virtual_interface_add: 10, p2p-dev-wlan0
[   12.535081] rwnx_virtual_interface_add, ifname=p2p-dev-wlan0, wdev=00000000c2903c2e, vif_idx=1
[   12.544292] p2p dev addr=88 0 33 77 b1 61
[   12.688083] P2P interface started
[   14.261160] rwnx_send_sm_connect_req drv_vif_index:0 connect to Arch(4) channel:5240 auth_type:0
[   23.130216] dw-apb-uart 41c0000.serial: failed to request DMA
[   23.665694] cvitek-i2s 4110000.i2s: Audio system clk=24576000, sample rate=16000
[   24.235991] vi_open:5865(): -
[   24.239413] vpss_open
[   24.275487] vi_release:5888(): -
[   24.278902] vpss_release
[   24.637690] debugfs: File 'force_bredr_smp' in directory 'hci0' already present!
