root@aby-box-arm:~# cat /proc/interrupts
           CPU0       
 11:       8894     GIC-0  27 Level     arch_timer
 14:          0     GIC-0  92 Edge      cvi-tpu-tdma
 19:          0     GIC-0 116 Edge      cvi-saradc
 21:        425     GIC-0  45 Level     dw_dmac
 23:         10     GIC-0  60 Level     ttyS0
 26:        325     GIC-0  64 Level     ttyS4
 27:          1     GIC-0  66 Level     4010000.i2c
 28:          0     GIC-0  67 Level     4020000.i2c
 29:          4     GIC-0  68 Level     4030000.i2c
 31:      17550     GIC-0  52 Level     mmc0
 32:       4874     GIC-0  54 Level     mmc1
 33:          0     GIC-0  56 Level     4100000.i2s
 34:          0     GIC-0  57 Level     4110000.i2s
 35:          0     GIC-0  58 Level     4120000.i2s
 36:          0     GIC-0  59 Level     4130000.i2s
 37:          0     GIC-0  42 Level     cif-irq0
 38:          0     GIC-0  43 Level     cif-irq1
 39:          0     GIC-0  40 Level     a000000.vi
 40:          0     GIC-0  41 Level     CVI_VIP_SCL
 41:          0     GIC-0 113 Edge      a0a0000.ive
 42:          0     GIC-0  44 Level     CVI_VIP_DWA
 43:          0     GIC-0  38 Level     h265
 44:          0     GIC-0  37 Level     h264
 45:          0     GIC-0  39 Level     sbm
 47:          0     GIC-0 117 Level     mailbox
 48:        727     GIC-0  46 Level     4340000.usb, 4340000.usb, dwc2_hsotg:usb1
 57:          0  gpio-dwapb   6 Edge      cviusb-otg
 58:          0  gpio-dwapb  13 Edge      cd-gpio-irq
IPI0:         0       Rescheduling interrupts
IPI1:         0       Function call interrupts
IPI2:         0       CPU stop interrupts
IPI3:         0       CPU stop (for crash dump) interrupts
IPI4:         0       Timer broadcast interrupts
IPI5:         0       IRQ work interrupts
IPI6:         0       CPU wake-up interrupts
Err:          0
root@aby-box-arm:~# cat /proc/iomem | grep -i dma
03000154-03000163 : 3000154.sysdma_remap sysdma_remap
04330000-04330fff : 4330000.dma dma@0x4330000
0c100000-0c100fff : c100000.tpu tdma

root@aby-box-arm:~# cat /proc/tty/driver/serial
serinfo:1.0 driver revision:
0: uart:16550A mmio:0x04140000 irq:23 tx:183 rx:0 RTS|DTR|DSR|CD|RI
1: uart:16550A mmio:0x04150000 irq:24 tx:0 rx:0 DSR|CD|RI
2: uart:unknown port:00000000 irq:0
3: uart:16550A mmio:0x04170000 irq:25 tx:0 rx:0 DSR|CD|RI
4: uart:16550A mmio:0x041C0000 irq:26 tx:3161 rx:1570 RTS|CTS|DTR|DSR|CD|RI
